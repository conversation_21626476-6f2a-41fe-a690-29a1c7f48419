import { useEffect, useState } from 'react'
import { EmbargoPermission, Permission, Permissions } from 'src/v2/models/AccessControlConsole'
import { PermissionEntity, embargoMap, permissionsMap } from '../utils/permissionsMaps'
import { transformPermissionNames } from '../utils/transformPermissionNames'

export interface FormattedPermission {
  permissionName: string
  userHasEmbargoException?: boolean
  user?: {
    title: string
    content: string[] | string | boolean
    extraInfo?: boolean
  }
  asset?: {
    title: string
    content: string[] | string | boolean
    extraInfo?: boolean
  }
  match: boolean
}

export const useGetFilteredPermissions = (permissionsData: Permissions, isCheckingMatch: boolean) => {
  const [formattedPermissions, setFormattedPermissions] = useState<FormattedPermission[]>([])

  function getMappedPermission (permissionName: string, type: PermissionEntity, originalContent: string[] | boolean, checkingMatch: boolean) {
    if (permissionName === 'embargo') {
      return embargoMap[permissionName][type](originalContent as boolean, checkingMatch)
    }

    return permissionsMap[permissionName][type](originalContent as string[], checkingMatch)
  }

  const generatePermissions = (isCheckingBoth: boolean) => {
    const availablePermissions: Array<[string, Permission | EmbargoPermission]> = Object.entries(permissionsData)
    const formattedPermissions: FormattedPermission[] = availablePermissions.map(([permissionName, value]) => {
      let transformed: FormattedPermission = {
        permissionName: transformPermissionNames(permissionName),
        user: value.user !== undefined ? getMappedPermission(permissionName, 'user', value.user ?? [], isCheckingBoth) : undefined,
        asset: value.asset !== undefined ? getMappedPermission(permissionName, 'asset', value.asset ?? [], isCheckingBoth) : undefined,
        match: value.match ?? undefined
      }

      if (permissionName === 'embargo') {
        const permissionValue = value as EmbargoPermission

        transformed = {
          ...transformed,
          userHasEmbargoException: permissionValue.userHasEmbargoException ?? undefined
        }

        return transformed
      }

      return transformed
    })

    setFormattedPermissions(formattedPermissions)
  }

  useEffect(() => {
    if (isCheckingMatch !== undefined && permissionsData) {
      generatePermissions(isCheckingMatch)
    }
  }, [permissionsData])

  return {
    formattedPermissions
  }
}
