import React, { useState, useEffect } from 'react'
import * as _ from 'lodash'
import { Form, Radio, Select, Spin } from 'antd'
import './IPAPermissions.scss'
import New from './../../../images/New.png'
import CustomLabel from './CustomLabel'

import useIPAPermissions from '../../hooks/useIPAPermissions'
import { FilterInternalUser } from './FilterInternalUsers'
import SelectInfinite from 'src/v2/sharedComponents/SelectInfinite/SelectInfinite'

const { Option } = Select

const IPAPermissions2 = (props) => {
  const {
    selectedItems,
    form: { setFieldsValue, isFieldTouched, getFieldDecorator },
    hasRegion = true,
    hasAccounts = false,
    hasGroups = false,
    hasUnrestricted = false,
    hasRP = true,
    handleSelectedPermission,
    selectedRequestPermission,
    onCancel
  } = props

  const [dataPermissions, isDataLoading] = useIPAPermissions({
    hasGroups,
    hasAccounts,
    hasRP,
    selectedItems
  })
  const [data, setData] = useState({
    selectedL3: [],
    typesRole: [],
    typesTier: [],
    typesRegions: [],
    typesGroups: [],
    typesAccounts: [],
    selectedRoles: [],
    selectedTiers: [],
    selectedGroups: [],
    selectedAccounts: [],
    selectedRegions: [],
    typeOfUser: null,
    internalSelection: [20],
    isInitialized: false
  })

  useEffect(() => {
    if (onCancel) {
      setInitialSelections()
    }
  }, [onCancel])

  useEffect(() => {
    if (!isDataLoading && !data.isInitialized) {
      setInitialSelections()
    }
  }, [isDataLoading])

  const setInitialSelections = () => {
    const {
      selectedItems,
      form: { setFieldsValue }
    } = props
    const { roles, tiers, regions, groups, accounts, internalUsers } = dataPermissions

    const filteredRoles = roles ? roles.filter((rol) => rol.value !== 53) : roles

    const selectedRoles = mappers.selectedL3(selectedItems, roles)
    const selectedTiers = mappers.selectedL3(selectedItems, tiers)
    const selectedRegions = mappers.selectedL3(selectedItems, regions)
    const typeOfUser = !selectedItems.length
      ? 0
      : selectedItems.length === 1 && selectedItems[0] === 20
      ? 1
      : selectedItems.length && selectedItems[0] === 71
      ? 3
      : 2
    const internalSelection = [20]

    setData({
      ...data,
      typesRole: filteredRoles,
      typesTier: tiers,
      typesRegions: regions,
      typesGroups: groups,
      typesAccounts: accounts,
      initialSelectedItems: props.selectedItems,
      selectedGroups: props.selectedGroups,
      selectedAccounts: props.selectedAccounts,
      selectedL3: selectedItems,
      selectedRoles,
      selectedTiers,
      selectedRegions,
      internalUserGroups: selectedRequestPermission,
      typeOfUser,
      internalSelection,
      isInitialized: true
    })

    setFieldsValue({
      roles: selectedRoles,
      tiers: selectedTiers,
      regions: selectedRegions
    })
    props.setIsDataLoading && props.setIsDataLoading(false)
  }

  const mappers = {
    selectedL3: (selectedItems, permissions) => {
      return selectedItems.filter((x) => permissions.map((x) => x.key).includes(x))
    }
  }

  const onTypeOfUserChange = (e) => {
    const { value } = e.target
    const l3 = value === 1 ? [20] : value === 3 ? [71, 20] : []
    setData({
      ...data,
      typeOfUser: value,
      selectedL3: l3,
      selectedRoles: [],
      selectedTiers: [],
      selectedRegions: [],
      selectedGroups: [],
      selectedAccounts: []
    })
    if (handleSelectedPermission) {
      handleSelectedPermission(l3[0])
    }
    setFieldsValue({ user_types_l3: l3 })
  }

  const onChangeL3 = (key, value) => {
    const l3 = getDataL3(key, value)
    setData({
      ...data,
      [key]: value,
      selectedL3: l3
    })

    setFieldsValue({ user_types_l3: l3 })
  }

  const getDataL3 = (key, value) => {
    const initialArray = [...data.internalSelection, ...value]
    const l3 = {
      selectedTiers: [...initialArray, ...data.selectedRegions, ...data.selectedRoles],
      selectedRoles: [...initialArray, ...data.selectedRegions, ...data.selectedTiers],
      selectedRegions: [...initialArray, ...data.selectedRoles, ...data.selectedTiers]
    }

    return l3[key]
  }

  const toolTips = {
    internalUserFilter: () => (
      <>
        Only users within an existing group of Internal Users are allowed to see this{' '}
        {props.material}. If none is selected, the {props.material} will have no restrictions.
        <br />
        <br />
        Users that are <u>not</u> part of this group <u>will have to request permission</u> in order
        to see the {props.material}.
      </>
    ),
    contentType: () => (
      <>
        This selection will determine wich partners are allowed to see this {props.material}t based
        on their role.
        <br />
        <br />
        Select all the content types that apply to this {props.material}. At least 1 type is
        required.
      </>
    ),
    contentMember: () => (
      <>
        This selection will determine wich partners are allowed to see this {props.material} based
        on their membership tier.
        <br />
        <br />
        Select all tiers that will see this {props.material}. At least 1 tier is required.
      </>
    ),
    contentGroups: () => (
      <>
        This selection will determine that only the users in within an existing group are allowed to
        see this {props.material}. If none is selected, the {props.material} will have no group
        restrictions.
        <br />
        <br />
        In order to view the {props.material}, the user must be within the scope of the main viewing
        permissions AND at the same time be included within the group.
      </>
    ),
    contentInfoAccount: () => (
      <>
        This selection will determine that only the users in the accounts selected are allowed to
        see this {props.material}. If none is selected, the {props.material} will have no account
        restrictions.
        <br />
        <br />
        In order to view the {props.material}, the user must be within the scope of the main viewing
        permissons AND at the same time be included within the account
      </>
    ),
    contentRegion: () => (
      <>
        This selection will determine that only the users in the accounts selected are allowed to
        see this {props.material}. If none is selected, the {props.material} will have no account
        restrictions.
        <br />
        <br />
        In order to view the {props.material}, the user must be within the scope of the main viewing
        permissons AND at the same time be included within the account
      </>
    )
  }

  return (
    <div className="viewing-permissions ipa-permissions">
      {data.isInitialized ? (
        <div>
          <Radio.Group onChange={(e) => onTypeOfUserChange(e)} value={data.typeOfUser}>
            <Radio value={1}>
              <b>Internal users only</b>(Intel blue/green badges)
            </Radio>
            <Radio value={2}>
              <b>Internal and external users</b>(Intel + partners)
            </Radio>
            {hasUnrestricted && (
              <Radio value={3}>
                <b>Unrestricted</b>(Public Asset - Registered or Unregistered users)
              </Radio>
            )}
          </Radio.Group>

          {data.typeOfUser === 1 && hasRP && (
            <div className="ipa-permissions-hideable">
              <h2>
                Select the user entitlement criteria for this {props.material}
                {!props.fromEmbargo && props.isEmbargoForm && ' under embargo'}:
              </h2>
              {hasRP && (
                <FilterInternalUser
                  data={data}
                  setData={setData}
                  getFieldDecorator={getFieldDecorator}
                  arrayOfGroups={dataPermissions.internalUsers.data}
                  toolTips={toolTips}
                  getDataL3={getDataL3}
                  setFieldsValue={setFieldsValue}
                />
              )}
            </div>
          )}
          {data.typeOfUser === 2 && (
            <div className="ipa-permissions-hideable">
              <h2>
                Select the user entitlement criteria for this {props.material}
                {!props.fromEmbargo && props.isEmbargoForm && ' under embargo'}:
              </h2>
              <Form.Item
                colon={false}
                label={
                  <CustomLabel required={true} label={'Content Type'} info={toolTips.contentType} />
                }
                className={`${
                  (data.selectedRoles.length || !isFieldTouched('roles')) && 'delete-error'
                }`}
              >
                {getFieldDecorator('roles', {
                  initialValue: data.selectedRoles,
                  rules: [
                    {
                      required: true,
                      message: 'At least 1 role is required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    onChange={(value) => onChangeL3('selectedRoles', value)}
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {data.typesRole &&
                      data.typesRole.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>

              <Form.Item
                colon={false}
                label={
                  <CustomLabel
                    required={true}
                    label={'Membership Tier'}
                    info={toolTips.contentMember}
                  />
                }
                className={`${
                  (data.selectedTiers.length || !isFieldTouched('tiers')) && 'delete-error'
                }`}
              >
                {getFieldDecorator('tiers', {
                  initialValue: data.selectedTiers,
                  rules: [
                    {
                      required: true,
                      message: 'At least 1 tier is required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    onChange={(value) => onChangeL3('selectedTiers', value)}
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {data.typesTier.map((f) => (
                      <Option key={f.key} value={f.value}>
                        {f.label}
                      </Option>
                    ))}
                  </Select>
                )}
              </Form.Item>

              {hasRegion && (
                <Form.Item
                  colon={false}
                  label={
                    <CustomLabel
                      required={true}
                      label={'Publish by region'}
                      info={toolTips.contentRegion}
                    />
                  }
                  className="delete-error"
                >
                  <img id="new-icon" src={New} alt="new-icon" />
                  {getFieldDecorator('regions', {
                    initialValue: data.selectedRegions,
                    rules: [{ required: true, message: 'At least 1 region is required' }]
                  })(
                    <Select
                      mode="multiple"
                      placeholder="Please select an option"
                      showSearch
                      onChange={(value) => onChangeL3('selectedRegions', value)}
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {data.typesRegions.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              )}

              {hasGroups && (
                <Form.Item
                  colon={false}
                  label={<CustomLabel label={'Publish by Group'} info={toolTips.contentGroups} />}
                  className="delete-error"
                >
                  <img id="new-icon" src={New} alt="new-icon" />

                  {getFieldDecorator('groups', {
                    initialValue: props.selectedGroups
                  })(
                    <Select
                      mode="multiple"
                      placeholder="Please select an option"
                      showSearch
                      onChange={(value) => {
                        setData({ ...data, selectedGroups: value })
                        setFieldsValue({ groups: value })
                      }}
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {data.typesGroups.map((f) => (
                        <Option key={f.value} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              )}
              {hasAccounts && (
                <Form.Item
                  wrapperCol={{ span: 13 }}
                  labelCol={{ span: 24 }}
                  colon={false}
                  label={
                    <CustomLabel label={'Publish by account'} info={toolTips.contentInfoAccount} />
                  }
                  className="delete-error"
                >
                  <img id="new-icon" src={New} alt="new-icon" />
                  {getFieldDecorator('accounts', {
                    initialValue: props.selectedAccounts
                  })(
                    <SelectInfinite
                      showArrow={true}
                      mode="multiple"
                      placeholder="Please select an option"
                      showSearch
                      onChange={(value) => {
                        setData({ ...data, selectedAccounts: value })
                        setFieldsValue({ accounts: value })
                      }}
                      optionsData={data.typesAccounts}
                    />
                  )}
                </Form.Item>
              )}
              {hasRP && (
                <FilterInternalUser
                  data={data}
                  setData={setData}
                  getFieldDecorator={getFieldDecorator}
                  arrayOfGroups={dataPermissions.internalUsers.data}
                  toolTips={toolTips}
                  getDataL3={getDataL3}
                />
              )}
            </div>
          )}
        </div>
      ) : (
        <div
          style={{ display: 'flex', justifyContent: 'center', width: '50%', alignItems: 'center' }}
        >
          <Spin />
        </div>
      )}
    </div>
  )
}

export default IPAPermissions2
